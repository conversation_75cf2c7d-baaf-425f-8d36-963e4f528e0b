<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\ExamTemporaryAnswer;
use App\Models\Student;
use App\Models\Answer;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CompleteExpiredExams extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:complete-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically complete all student exams when exam time expires';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting expired exam completion check...');

        try {
            // Get all exams that have expired (current time is past end date + end time)
            $now = Carbon::now();
            $expiredExams = Exam::where(function ($query) use ($now) {
                $query->where('enddate', '<', $now->toDateString())
                    ->orWhere(function ($subQuery) use ($now) {
                        $subQuery->where('enddate', '=', $now->toDateString())
                            ->where('endtime', '<', $now->toTimeString());
                    });
            })->get();

            $this->info("Found {$expiredExams->count()} expired exams");

            $completedCount = 0;

            foreach ($expiredExams as $exam) {
                $this->info("Processing exam: {$exam->name} (ID: {$exam->id})");

                // Get all students in the exam's class
                $students = Student::where('classid', $exam->classid)->get();

                foreach ($students as $student) {
                    // Check if student already has a completed exam result
                    $existingResult = ExamResult::where('examid', $exam->id)
                        ->where('studentid', $student->id)
                        ->first();

                    if (!$existingResult) {
                        // Student hasn't completed the exam, auto-complete it
                        $this->completeStudentExam($exam, $student);
                        $completedCount++;
                        $this->info("Auto-completed exam for student: {$student->name}");
                    }
                }
            }

            $this->info("Completed {$completedCount} student exams");
            Log::info("Exam completion cron job completed. Processed {$expiredExams->count()} expired exams, completed {$completedCount} student exams.");
        } catch (\Exception $e) {
            $this->error("Error in exam completion: " . $e->getMessage());
            Log::error("Error in exam completion cron job: " . $e->getMessage());
        }

        return 0;
    }

    /**
     * Complete a student's exam automatically
     */
    private function completeStudentExam(Exam $exam, Student $student)
    {
        DB::transaction(function () use ($exam, $student) {
            // Get student's temporary answers
            $temporaryAnswers = ExamTemporaryAnswer::where('examid', $exam->id)
                ->where('studentid', $student->id)
                ->get();

            // Calculate scores for each question type
            $scores = [
                'pilihan_ganda' => 0,
                'uraian_singkat' => 0,
                'esai' => 0
            ];

            $questionCounts = [
                'pilihan_ganda' => 0,
                'uraian_singkat' => 0,
                'esai' => 0
            ];

            // Get all exam questions grouped by type
            $examQuestions = $exam->examDetails()->with('question')->get();

            foreach ($examQuestions as $examDetail) {
                $question = $examDetail->question;
                $questionType = $question->type;
                $questionCounts[$questionType]++;

                // Find student's answer for this question
                $studentAnswer = $temporaryAnswers->where('questionid', $question->id)->first();

                if ($studentAnswer) {
                    if ($questionType === 'pilihan_ganda') {
                        // Check if multiple choice answer is correct
                        $correctAnswer = Answer::where('questionid', $question->id)
                            ->where('is_correct', true)
                            ->first();

                        if ($correctAnswer && $studentAnswer->answer === $correctAnswer->answer) {
                            $scores[$questionType]++;
                        }
                    } elseif ($questionType === 'uraian_singkat') {
                        // Check if short answer matches exactly (case-insensitive)
                        $correctAnswer = Answer::where('questionid', $question->id)->first();

                        if (
                            $correctAnswer &&
                            strtolower(trim($studentAnswer->answer)) === strtolower(trim($correctAnswer->answer))
                        ) {
                            $scores[$questionType]++;
                        }
                    }
                    // Note: Essay questions (esai) are not auto-scored, they need manual review
                }
            }

            // Calculate final score based on the scoring system
            $totalScore = 0;
            $maxScore = 100;

            foreach (['pilihan_ganda', 'uraian_singkat', 'esai'] as $type) {
                if ($questionCounts[$type] > 0) {
                    $scorePerQuestion = ($maxScore / 3) / $questionCounts[$type]; // Divide equally among question types
                    $totalScore += $scores[$type] * $scorePerQuestion;
                }
            }

            // Create exam result
            ExamResult::create([
                'examid' => $exam->id,
                'studentid' => $student->id,
                'score' => round($totalScore, 2),
                'pilihan_ganda_score' => $scores['pilihan_ganda'],
                'uraian_singkat_score' => $scores['uraian_singkat'],
                'esai_score' => $scores['esai'], // Will be 0 for auto-completed exams
                'status' => 'completed',
                'completed_at' => Carbon::now(),
                'auto_completed' => true
            ]);
        });
    }
}
